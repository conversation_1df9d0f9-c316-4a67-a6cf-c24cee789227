import ollama from 'ollama';

export class Agent {
  /**
   * Creates a new Agent instance
   * @param {Object} config - Configuration options
   * @param {string} config.model - The model to use (default: 'gemma3:1b')
   * @param {string} config.systemInstruction - System instruction (default: 'You are a helpful assistant.')
   * @param {number} config.temperature - Temperature for response generation (default: 0.7)
   */
  constructor(config = {}) {
    this.model = config.model || 'gemma3:1b';
    this.systemInstruction = config.systemInstruction || 'You are a helpful assistant.';
    this.temperature = config.temperature !== undefined ? config.temperature : 0.7;
    this.messages = [];
  }

  /**
   * Sets or updates the system instruction
   * @param {string} instruction - The system instruction to use
   */
  setSystemInstruction(instruction) {
    this.systemInstruction = instruction;
    return this; // Allow for method chaining
  }

  /**
   * Sets or updates the model
   * @param {string} model - The model to use
   */
  setModel(model) {
    this.model = model;
    return this; // Allow for method chaining
  }

  /**
   * Sets or updates the temperature
   * @param {number} temperature - The temperature to use
   */
  setTemperature(temperature) {
    this.temperature = temperature;
    return this; // Allow for method chaining
  }

  /**
   * Adds a user message to the conversation
   * @param {string} content - The message content
   */
  addUserMessage(content) {
    this.messages.push({ role: 'user', content });
    return this; // Allow for method chaining
  }

  /**
   * Adds an assistant message to the conversation
   * @param {string} content - The message content
   */
  addAssistantMessage(content) {
    this.messages.push({ role: 'assistant', content });
    return this; // Allow for method chaining
  }

  /**
   * Clears all messages from the conversation history
   */
  clearMessages() {
    this.messages = [];
    return this; // Allow for method chaining
  }

  /**
   * Gets the full conversation history including system instruction
   * @returns {Array} The full conversation array
   */
  getConversation() {
    return [
      { role: 'system', content: this.systemInstruction },
      ...this.messages
    ];
  }

  /**
   * Runs the chat with the current configuration and messages
   * @param {Object} options - Additional options
   * @param {boolean} options.stream - Whether to stream the response (default: true)
   * @returns {Promise} The response from the model
   */
  async run({ stream = true } = {}) {
    const conversation = this.getConversation();

    const response = await ollama.chat({
      model: this.model,
      messages: conversation,
      stream,
      temperature: this.temperature,
    });

    return response;
  }

  /**
   * Runs the chat and streams the response to stdout
   * @param {Object} options - Additional options
   */
  async runAndStream({ stream = true } = {}) {
    const response = await this.run({ stream });

    if (stream) {
      for await (const part of response) {
        process.stdout.write(part.message.content);
      }
    } else {
      // If not streaming, just print the full response
      const fullResponse = await response;
      process.stdout.write(fullResponse.message.content);
    }
  }
}

