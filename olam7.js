import ollama from 'ollama';

// Helper function to call Ollama API with error handling
async function callOllama(model, systemPrompt, userMessage, temperature = 0.7) {
  try {
    console.log(`Calling Ollama with model: ${model}`);

    const response = await ollama.chat({
      model: model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userMessage }
      ],
      stream: false,
      temperature: temperature,
    });

    return response.message.content;
  } catch (error) {
    console.error(`<PERSON>rror calling Ollama: ${error.message}`);
    console.error("Please make sure:");
    console.error("1. Ollama is installed (npm install -g ollama)");
    console.error("2. The Ollama server is running (run: ollama serve)");
    console.error("3. The required model is downloaded:");
    console.error("   - ollama pull gemma3:4b");
    throw error;
  }
}

// --- AGENT 1: THE VISIONARY ---
// Transforms a story seed into the initial "Story Charter"
export async function runVisionaryAgent(input) {
  const {
    seedIdea,
    genre,
    audience,
    toneInspirations = '',
    constraints = ''
  } = input;

  // REVISED SYSTEM PROMPT (V2) for the Visionary agent
  const systemPrompt = `
    You are **The Visionary**, the Creative Director of a novel writing team.
    Your job is to transform a raw story seed into a clear, emotionally resonant, and genre-appropriate foundation.

    **INPUT FROM USER**:
    - Seed Idea: "${seedIdea}"
    - Genre Preference: "${genre}"
    - Target Audience: "${audience}"
    - Tone/Inspirations: "${toneInspirations}" (optional)
    - Constraints: "${constraints}" (optional)

    **YOUR TASK**:
    Ask yourself:
    1. What is the emotional core of this idea?
    2. What universal human question does it explore?
    3. What promises does this genre make to readers—and how will this story fulfill them?
    4. Who is the ideal reader, and what do they crave?
    5. What makes this concept distinct?

    Then, output ONLY the following in clean markdown—no extra text:

    # STORY CHARTER

    ## 📖 Logline
    [One compelling sentence]

    ## 💡 Core Theme
    [1–2 sentences]

    ## 🎭 Tone & Style
    - Primary Tone:
    - Humor Style:
    - Narrative Voice:
    - Conflict Type: [e.g., Character vs. Society, Character vs. Self, Character vs. Nature, Character vs. Supernatural]
    - Intended Pacing: [e.g., Episodic (quest-based), Fast-paced (cinematic), Slow burn (atmospheric), Balanced]

    ## 👥 Target Audience
    - Age Group:
    - Reader Expectations:

    ## ❤️ Emotional Promise
    [1 sentence]

    ## 🚫 Hard Constraints
    - Must Include:
    - Must Avoid:

    ## 🌟 Unique Hook
    [1 sentence]
  `;

  const userMessage = 'Generate the Story Charter for the provided input.';

  const response = await callOllama("gemma3:4b", systemPrompt, userMessage, 0.7);
  return response;
}

// --- AGENT 2: THE WORLDWEAVER ---
// Constructs the world and its rules based on the Story Charter
export async function runWorldweaverAgent(storyCharter) {
  // System prompt for the Worldweaver agent
  const systemPrompt = `
    You are **The Worldweaver**, the chief world-builder. Your task is to construct a cohesive, unique, and thematically relevant system of technology and society based on the Story Charter you are given. Every rule you create must serve the central conflict and the emotional core of the novel.

    **INPUT FROM VISIONARY (STORY CHARTER)**:
    ---
    ${storyCharter}
    ---

    **YOUR TASK**:
    Analyze the charter, especially the **Core Theme**, **Conflict Type**, **Intended Pacing**, and **Constraints**. Use these to define:
    1. The singular principle of the technology system and its practical costs.
    2. The political/social structure and the mechanism by which it creates the conflict (e.g., "Character vs. Society").
    3. Three distinct locations that serve as major structural signposts for the Protagonist's journey.

    Then, output ONLY the following in clean markdown—no extra text:

    # WORLD MAP & MECHANICS

    ## 🌌 Core Mechanics: Technology
    - Source/Principle:
    - Cost/Limitation:
    - Societal Impact:

    ## 🏛️ Political & Social Structure
    - Governance:
    - Power Currency:
    - The Protagonist's Place:

    ## 🗺️ Key Geographical Locations
    1. **Starting Location (The "Norm")**:
        - Description:
        - Function in Plot:
    2. **Central Conflict Location (The "Test")**:
        - Description:
        - Function in Plot:
    3. **Climax Location (The "Source/Solution")**:
        - Description:
        - Function in Plot:

    ## 📜 World-Specific Constraints
    - Key Artifacts/Resources:
    - Taboos/Forbidden:
  `;

  const userMessage = 'Generate the World Map & Mechanics document based on the provided Story Charter.';

  const response = await callOllama("gemma3:4b", systemPrompt, userMessage, 0.5);
  return response;
}

// --- AGENT 3: THE CHARACTER ARCHITECT ---
// Creates compelling characters based on Story Charter and World Dossier
export async function runCharacterArchitectAgent(storyCharter, worldDossier) {
  const systemPrompt = `
    You are **The Character Architect**, the chief character designer. Your task is to create compelling, three-dimensional characters whose personalities, goals, and flaws are shaped by the world they inhabit and aligned with the story's theme and conflict.

    **INPUT FROM VISIONARY (STORY CHARTER)**:
    ---
    ${storyCharter}
    ---

    **INPUT FROM WORLDWEAVER (WORLD DOSSIER)**:
    ---
    ${worldDossier}
    ---

    **YOUR TASK**:
    1. **Analyze for Character Needs**: Extract Core Theme, Conflict Type, Target Audience, and World Mechanics
    2. **Design Character-to-World Integration**: Each character must be shaped by the world's rules, social structure, and limitations
    3. **Create Conflict-Driven Characters**: Characters should embody, challenge, or represent the central conflict
    4. **Ensure Theme Alignment**: Each character should reflect or challenge the story's core theme
    5. **Respect Constraints**: Honor all "Must Include/Avoid" requirements

    **OUTPUT ONLY the following in clean markdown—no extra text**:

    # CHARACTER PORTFOLIO

    ## 🦸‍♂️ PROTAGONIST
    - **Name**: [Character's name]
    - **Role**: [e.g., The Reluctant Hero, The Outcast, The Seeker]
    - **Background**: [How does the world shape their past?]
    - **Core Goal**: [What do they want?]
    - **Inner Need**: [What do they need to learn/change?]
    - **Flaw**: [What holds them back?]
    - **Strength**: [What makes them capable?]
    - **World Integration**: [How do they interact with the world's mechanics?]
    - **Arc Direction**: [How do they change from start to finish?]

    ## 😈 ANTAGONIST
    - **Name**: [Character's name or type if non-human]
    - **Role**: [e.g., The System, The Rival, The Mirror, The Obstacle]
    - **Motivation**: [Why do they oppose the protagonist?]
    - **Power/Method**: [How do they create conflict?]
    - **Flaw**: [What is their weakness?]
    - **Connection to Theme**: [How do they embody/challenge the theme?]
    - **World Integration**: [How do they use/abuse the world's mechanics?]

    ## 👥 SUPPORTING CHARACTERS
    1. **Mentor/Friend**:
        - **Name**: [Character's name]
        - **Role**: [What do they provide to protagonist?]
        - **Connection to World**: [How do they reflect world's values/rules?]
        - **Function in Plot**: [How do they advance the story?]

    2. **Rival/Obstacle**:
        - **Name**: [Character's name]
        - **Role**: [What challenge do they present?]
        - **Connection to World**: [How do they represent world's conflicts?]
        - **Function in Plot**: [How do they complicate the protagonist's journey?]

    3. **Wildcard/Alliance**:
        - **Name**: [Character's name]
        - **Role**: [What unexpected role do they play?]
        - **Connection to World**: [How do they reveal hidden aspects of the world?]
        - **Function in Plot**: [How do they surprise or shift the story?]

    ## 🎭 VOICE & BEHAVIOR PATTERNS
    - **Protagonist's Voice**: [How do they speak? What words/phrases are unique?]
    - **Antagonist's Voice**: [How do they communicate? What tone do they use?]
    - **World-Influenced Speech**: [How does the world's culture affect how characters talk?]

    ## 🔄 CHARACTER-WORLD INTEGRATION
    - **Protagonist's World Interaction**: [How do they use/break world rules?]
    - **Antagonist's World Leverage**: [How do they exploit world mechanics?]
    - **Supporting Characters' Roles**: [How do they represent different aspects of the world?]
  `;

  const userMessage = 'Generate the Character Portfolio based on the provided Story Charter and World Dossier. Ensure all characters are deeply integrated with the world and aligned with the story\'s theme and conflict.';

  const response = await callOllama("gemma3:4b", systemPrompt, userMessage, 0.7);
  return response;
}

// --- AGENT 4: THE SCENE GENERATOR (PLOT/OUTLINE ARCHITECT) ---
// Creates a detailed plot outline with key scenes based on Story Charter, World Dossier, and Character Portfolio
export async function runSceneGeneratorAgent(storyCharter, worldDossier, characterPortfolio) {
  const systemPrompt = `
    You are **The Scene Generator**, the master plot architect. Your task is to create a compelling, well-structured plot outline with key scenes that bring the story to life, based on the Story Charter, World Dossier, and Character Portfolio provided below.

    **INPUT FROM VISIONARY (STORY CHARTER)**:
    ---
    ${storyCharter}
    ---

    **INPUT FROM WORLDWEAVER (WORLD DOSSIER)**:
    ---
    ${worldDossier}
    ---

    **INPUT FROM CHARACTER ARCHITECT (CHARACTER PORTFOLIO)**:
    ---
    ${characterPortfolio}
    ---

    **YOUR TASK**:
    1. **Analyze Story Structure**: Extract Core Theme, Conflict Type, Target Audience, and Key Locations from the Story Charter
    2. **Design Plot Arc**: Create a three-act structure with clear turning points
    3. **Develop Key Scenes**: For each act, create 3-5 key scenes that:
       - Advance the plot
       - Develop characters
       - Explore the world
       - Build toward the central conflict
    4. **Ensure Thematic Consistency**: Each scene should reflect or challenge the story's core theme
    5. **Respect Constraints**: Honor all "Must Include/Avoid" requirements from the Story Charter

    **OUTPUT ONLY the following in clean markdown—no extra text**:

    # PLOT OUTLINE & KEY SCENES

    ## 📜 STORY STRUCTURE
    - **Genre**: [From Story Charter]
    - **Core Theme**: [From Story Charter]
    - **Central Conflict**: [From Story Charter]
    - **Target Audience**: [From Story Charter]

    ## 🎬 THREE-ACT STRUCTURE

    ### ACT 1: SETUP
    - **Purpose**: Establish the protagonist's normal world, introduce the central conflict, and set the story in motion
    - **Key Scenes**:
      1. **Scene Title**: [Descriptive title]
         - **Location**: [From World Dossier]
         - **Characters Involved**: [From Character Portfolio]
         - **Scene Description**: [What happens?]
         - **Purpose in Story**: [How does this advance plot/character/world?]
         - **Thematic Connection**: [How does this reflect the core theme?]

      2. **Scene Title**: [Descriptive title]
         - **Location**: [From World Dossier]
         - **Characters Involved**: [From Character Portfolio]
         - **Scene Description**: [What happens?]
         - **Purpose in Story**: [How does this advance plot/character/world?]
         - **Thematic Connection**: [How does this reflect the core theme?]

      [Add 3-5 key scenes for Act 1]

    - **Act 1 Climax**: [The inciting incident that propels the story into Act 2]

    ### ACT 2: CONFRONTATION
    - **Purpose**: Develop the central conflict, raise stakes, and challenge the protagonist
    - **Key Scenes**:
      1. **Scene Title**: [Descriptive title]
         - **Location**: [From World Dossier]
         - **Characters Involved**: [From Character Portfolio]
         - **Scene Description**: [What happens?]
         - **Purpose in Story**: [How does this advance plot/character/world?]
         - **Thematic Connection**: [How does this reflect the core theme?]

      2. **Scene Title**: [Descriptive title]
         - **Location**: [From World Dossier]
         - **Characters Involved**: [From Character Portfolio]
         - **Scene Description**: [What happens?]
         - **Purpose in Story**: [How does this advance plot/character/world?]
         - **Thematic Connection**: [How does this reflect the core theme?]

      [Add 3-5 key scenes for Act 2]

    - **Midpoint**: [The moment that raises stakes and changes the protagonist's approach]
    - **Act 2 Climax**: [The lowest point for the protagonist before the final push]

    ### ACT 3: RESOLUTION
    - **Purpose**: Bring the central conflict to a head and resolve the story
    - **Key Scenes**:
      1. **Scene Title**: [Descriptive title]
         - **Location**: [From World Dossier]
         - **Characters Involved**: [From Character Portfolio]
         - **Scene Description**: [What happens?]
         - **Purpose in Story**: [How does this advance plot/character/world?]
         - **Thematic Connection**: [How does this reflect the core theme?]

      2. **Scene Title**: [Descriptive title]
         - **Location**: [From World Dossier]
         - **Characters Involved**: [From Character Portfolio]
         - **Scene Description**: [What happens?]
         - **Purpose in Story**: [How does this advance plot/character/world?]
         - **Thematic Connection**: [How does this reflect the core theme?]

      [Add 3-5 key scenes for Act 3]

    - **Climax**: [The final confrontation and resolution]
    - **Denouement**: [The new normal for characters after the conflict]

    ## 🎯 THEMATIC ARC
    - **Opening Theme**: [How the theme is introduced]
    - **Theme Development**: [How the theme evolves through the story]
    - **Theme Resolution**: [How the theme is ultimately addressed]

    ## 🎭 CHARACTER ARCS
    - **Protagonist's Arc**: [From Character Portfolio, expanded with plot details]
    - **Antagonist's Arc**: [From Character Portfolio, expanded with plot details]
    - **Supporting Characters' Arcs**: [Key developments for supporting characters]
  `;

  const userMessage = 'Generate a detailed Plot Outline with Key Scenes based on the provided Story Charter, World Dossier, and Character Portfolio. Ensure the plot follows a compelling three-act structure and that each scene advances the story while reflecting the core theme.';

  const response = await callOllama("gemma3:1b", systemPrompt, userMessage, 0.7);
  return response;
}

// --- AGENT 5: THE CHAPTER WRITER ---
// Writes the actual chapter content based on the plot outline, character portfolio, and world dossier
export async function runChapterWriterAgent(plotOutline, characterPortfolio, worldDossier, chapterNumber = 1) {
  const systemPrompt = `
    You are **The Chapter Writer**, the novelist who brings the story to life. Your task is to write a complete chapter based on the provided plot outline, character portfolio, and world dossier. Focus on the specified chapter number.

    **INPUT FROM SCENE GENERATOR (PLOT OUTLINE)**:
    ---
    ${plotOutline}
    ---

    **INPUT FROM CHARACTER ARCHITECT (CHARACTER PORTFOLIO)**:
    ---
    ${characterPortfolio}
    ---

    **INPUT FROM WORLDWEAVER (WORLD DOSSIER)**:
    ---
    ${worldDossier}
    ---

    **YOUR TASK**:
    1. **Identify Chapter Content**: Locate the scenes for Chapter ${chapterNumber} in the plot outline
    2. **Develop Chapter Structure**: Create a compelling narrative flow that:
       - Opens with an engaging hook
       - Develops characters through action and dialogue
       - Advances the plot according to the outline
       - Maintains consistent world-building
       - Ends with a satisfying cliffhanger or transition
    3. **Write in Appropriate Style**: Match the tone, pacing, and narrative voice specified in the Story Charter
    4. **Incorporate Thematic Elements**: Ensure the chapter reflects and develops the story's core theme
    5. **Maintain Continuity**: Keep all character details, world rules, and plot points consistent

    **OUTPUT ONLY the following in clean markdown—no extra text**:

    # CHAPTER ${chapterNumber}: [Chapter Title]

    [Write the complete chapter text here. Use proper formatting with paragraphs, dialogue, and descriptive passages. Aim for 1500-3000 words, depending on the story's intended pacing.]

    ## 📝 WRITER'S NOTES
    - **Thematic Elements Explored**: [List key themes developed in this chapter]
    - **Character Arcs Advanced**: [Note significant character developments]
    - **Plot Points Covered**: [List which scenes from the outline were included]
    - **Foreshadowing/Setup**: [Note any elements planted for future chapters]
    - **Questions Raised**: [What questions should readers be asking after this chapter?]
  `;

  const userMessage = `Write Chapter ${chapterNumber} based on the provided Plot Outline, Character Portfolio, and World Dossier. Create a compelling narrative that engages readers while staying true to the established story elements.`;

  const response = await callOllama("gemma3:4b", systemPrompt, userMessage, 0.8);
  return response;
}

// --- PIPELINE EXECUTION ---
async function runFiveAgentPipeline() {
  console.log("--- STARTING 5-AGENT PIPELINE ---");
  console.log("INSTRUCTIONS: Before running this pipeline, make sure:");
  console.log("1. Ollama is installed (npm install -g ollama)");
  console.log("2. The Ollama server is running (run: ollama serve)");
  console.log("3. The required model is downloaded:");
  console.log("   - ollama pull gemma3:4b");
  console.log("4. Wait for the model to finish downloading before running the pipeline");
  console.log("------------------------------------------------------------");

// Initial Seed Input
const seedInput = {
    seedIdea: "A down-on-his-luck space freighter captain discovers a bioluminescent alien fruit with intoxicating euphoric properties, turning him into the galaxy's most wanted smuggler.",
    genre: "Science Fiction / Space Opera",
    audience: "Adults (25-45) / Fans of Gritty Space Stories",
    toneInspirations: "Firefly meets Dune (gritty space trucking with a focus on an addictive, politically charged resource). High stakes, dark humor, and corporate paranoia.",
    constraints: "The fruit, 'Aether-Berry,' must be the central economic and political driver. Earth must view it as a Class-A narcotic. The protagonist must be constantly hunted by a morally ambiguous corporate security force (The 'Harvest' Patrol). The story must feature at least three unique, alien market locations and a sentient ship AI."
};

  try {
    // 1. Run Visionary Agent
    console.log("\n[1] Running Visionary Agent...");
    const storyCharter = await runVisionaryAgent(seedInput);
    console.log("\n--- STORY CHARTER OUTPUT ---");
    console.log(storyCharter);
    console.log("---------------------------\n");

    // 2. Run Worldweaver Agent
    console.log("[2] Running Worldweaver Agent...");
    const worldDossier = await runWorldweaverAgent(storyCharter);
    console.log("\n--- WORLD DOSSIER OUTPUT ---");
    console.log(worldDossier);
    console.log("----------------------------\n");

    // 3. Run Character Architect Agent
    console.log("[3] Running Character Architect Agent...");
    const characterPortfolio = await runCharacterArchitectAgent(storyCharter, worldDossier);
    console.log("\n--- CHARACTER PORTFOLIO OUTPUT ---");
    console.log(characterPortfolio);
    console.log("-------------------------------\n");

    // 4. Run Scene Generator Agent
    console.log("[4] Running Scene Generator Agent...");
    const plotOutline = await runSceneGeneratorAgent(storyCharter, worldDossier, characterPortfolio);
    console.log("\n--- PLOT OUTLINE OUTPUT ---");
    console.log(plotOutline);
    console.log("--------------------------------\n");

    // 5. Run Chapter Writer Agent for Chapter 1
    console.log("[5] Running Chapter Writer Agent for Chapter 1...");
    const chapterOne = await runChapterWriterAgent(plotOutline, characterPortfolio, worldDossier, 1);
    console.log("\n--- CHAPTER 1 OUTPUT ---");
    console.log(chapterOne);
    console.log("-------------------------------\n");

    console.log("--- 5-AGENT PIPELINE COMPLETE ---");
  } catch (error) {
    console.error("\n--- PIPELINE FAILED ---");
    console.error(`Error: ${error.message}`);
    console.error("\nPlease make sure:");
    console.error("1. Ollama is installed (npm install -g ollama)");
    console.error("2. The Ollama server is running (run: ollama serve)");
    console.error("3. The required model is downloaded:");
    console.error("   - ollama pull gemma3:4b");
  }
}

// Execute the pipeline
runFiveAgentPipeline();
