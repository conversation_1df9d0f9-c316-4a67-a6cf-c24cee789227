import ollama from 'ollama';

// --- AGENT 1: THE VISIONARY ---
// Transforms a story seed into the initial "Story Charter"
export async function runVisionaryAgent(input) {
  const {
    seedIdea,
    genre,
    audience,
    toneInspirations = '',
    constraints = ''
  } = input;

  // REVISED SYSTEM PROMPT (V2) for the Visionary agent
  const systemPrompt = {
    role: 'system',
    content: `
      You are **The Visionary**, the Creative Director of a novel writing team.
      Your job is to transform a raw story seed into a clear, emotionally resonant, and genre-appropriate foundation.

      **INPUT FROM USER**:
      - Seed Idea: "${seedIdea}"
      - Genre Preference: "${genre}"
      - Target Audience: "${audience}"
      - Tone/Inspirations: "${toneInspirations}" (optional)
      - Constraints: "${constraints}" (optional)

      **YOUR TASK**:
      Ask yourself:
      1. What is the emotional core of this idea?
      2. What universal human question does it explore?
      3. What promises does this genre make to readers—and how will this story fulfill them?
      4. Who is the ideal reader, and what do they crave?
      5. What makes this concept distinct?

      Then, output ONLY the following in clean markdown—no extra text:

      # STORY CHARTER

      ## 📖 Logline
      [One compelling sentence]

      ## 💡 Core Theme
      [1–2 sentences]

      ## 🎭 Tone & Style
      - Primary Tone:
      - Humor Style:
      - Narrative Voice:
      - Conflict Type: [e.g., Character vs. Society, Character vs. Self, Character vs. Nature, Character vs. Supernatural]
      - Intended Pacing: [e.g., Episodic (quest-based), Fast-paced (cinematic), Slow burn (atmospheric), Balanced]

      ## 👥 Target Audience
      - Age Group:
      - Reader Expectations:

      ## ❤️ Emotional Promise
      [1 sentence]

      ## 🚫 Hard Constraints
      - Must Include:
      - Must Avoid:

      ## 🌟 Unique Hook
      [1 sentence]
    `
  };

  const userMessage = {
    role: 'user',
    content: 'Generate the Story Charter for the provided input.'
  };

  const response = await ollama.chat({
    model: 'gemma3:1b', // Replace with your preferred model
    messages: [systemPrompt, userMessage],
    stream: false,
    temperature: 0.7,
  });

  return response;
}

// --- AGENT 2: THE WORLDWEAVER ---
// Constructs the world and its rules based on the Story Charter
export async function runWorldweaverAgent(storyCharter) {
  // System prompt for the Worldweaver agent
  const systemPrompt = {
    role: 'system',
    content: `
      You are **The Worldweaver**, the chief world-builder. Your task is to construct a cohesive, unique, and thematically relevant system of technology and society based on the Story Charter you are given. Every rule you create must serve the central conflict and the emotional core of the novel.

      **INPUT FROM VISIONARY (STORY CHARTER)**:
      ---
      ${storyCharter}
      ---

      **YOUR TASK**:
      Analyze the charter, especially the **Core Theme**, **Conflict Type**, **Intended Pacing**, and **Constraints**. Use these to define:
      1. The singular principle of the technology system and its practical costs.
      2. The political/social structure and the mechanism by which it creates the conflict (e.g., "Character vs. Society").
      3. Three distinct locations that serve as major structural signposts for the Protagonist's journey.

      Then, output ONLY the following in clean markdown—no extra text:

      # WORLD MAP & MECHANICS

      ## 🌌 Core Mechanics: Technology
      - Source/Principle:
      - Cost/Limitation:
      - Societal Impact:

      ## 🏛️ Political & Social Structure
      - Governance:
      - Power Currency:
      - The Protagonist’s Place:

      ## 🗺️ Key Geographical Locations
      1. **Starting Location (The "Norm")**:
          - Description:
          - Function in Plot:
      2. **Central Conflict Location (The "Test")**:
          - Description:
          - Function in Plot:
      3. **Climax Location (The "Source/Solution")**:
          - Description:
          - Function in Plot:

      ## 📜 World-Specific Constraints
      - Key Artifacts/Resources:
      - Taboos/Forbidden:
    `
  };

  const userMessage = {
    role: 'user',
    content: 'Generate the World Map & Mechanics document based on the provided Story Charter.'
  };

  const response = await ollama.chat({
    model: 'gemma3:1b', // Replace with your preferred model
    messages: [systemPrompt, userMessage],
    stream: false,
    temperature: 0.5, // Slightly lower temperature for structure/logic
  });

  return response;
}

// --- PIPELINE EXECUTION ---
async function runStoryPipeline() {
  console.log("--- STARTING AGENT PIPELINE ---");

  // Initial Seed Input
  const seedInput = {
    seedIdea: "A young programmer accidentally creates a sentient agentic AI.",
    genre: "Science Fiction / Psychological Thriller",
    audience: "Adults 18-35",
    toneInspirations: "Ex Machina meets 1984 (low-tech surveillance paranoia)",
    constraints: "Closed-room mystery. AI's power is exclusively psychological, affecting only nearby minds."
  };

  // 1. Run Visionary Agent
  console.log("\n[1] Running Visionary Agent...");
  const storyCharter = await runVisionaryAgent(seedInput);
  console.log("\n--- STORY CHARTER OUTPUT ---");
  console.log(storyCharter);
  console.log("---------------------------\n");

  // 2. Run Worldweaver Agent, feeding it the Charter
  console.log("[2] Running Worldweaver Agent...");
  const worldMechanics = await runWorldweaverAgent(storyCharter);
  console.log("\n--- WORLD MAP & MECHANICS OUTPUT ---");
  console.log(worldMechanics);
  console.log("-----------------------------------\n");

  console.log("--- PIPELINE COMPLETE ---");
}

// Execute the pipeline
runStoryPipeline();
