import { GoogleGenAI } from "@google/genai";
import dotenv from "dotenv";
dotenv.config();

const ai = new GoogleGenAI({
    apiKey: process.env.GENAI_API_KEY,
});

// Visionary Agent: Transforms a story seed into a "Story Charter"
export async function runVisionaryAgent(input) {
  const {
    seedIdea,
    genre,
    audience,
    toneInspirations = '',
    constraints = ''
  } = input;

  // System prompt for the Visionary agent
  const systemPrompt = `
    You are **The Visionary**, the Creative Director of a fantasy novel writing team.
    Your job is to transform a raw story seed into a clear, emotionally resonant, and genre-appropriate foundation.

    **INPUT FROM USER**:
    - Seed Idea: "${seedIdea}"
    - Genre Preference: "${genre}"
    - Target Audience: "${audience}"
    - Tone/Inspirations: "${toneInspirations}" (optional)
    - Constraints: "${constraints}" (optional)

    **YOUR TASK**:
    Ask yourself:
    1. What is the emotional core of this idea?
    2. What universal human question does it explore?
    3. What promises does this genre make to readers—and how will this story fulfill them?
    4. Who is the ideal reader, and what do they crave?
    5. What makes this concept distinct?

    Then, output ONLY the following in clean markdown—no extra text:

    # STORY CHARTER

    ## 📖 Logline
    [One compelling sentence]

    ## 💡 Core Theme
    [1–2 sentences]

    ## 🎭 Tone & Style
    - Primary Tone:
    - Humor Style:
    - Narrative Voice:

    ## 👥 Target Audience
    - Age Group:
    - Reader Expectations:

    ## ❤️ Emotional Promise
    [1 sentence]

    ## 🚫 Hard Constraints
    - Must Include:
    - Must Avoid:

    ## 🌟 Unique Hook
    [1 sentence]
  `;

  // User message (empty, as the system prompt contains all input)
  const userMessage = 'Generate the Story Charter for the provided input.';

  // Call GoogleGenAI
  const response = await ai.models.generateContent({
    model: "gemma-3-1b-it",
    contents: [
      { text: systemPrompt },
      { text: userMessage }
    ],
    config: {
      temperature: 0.7
    }
  });

  return response.text;
}

// Example usage (for testing)
async function testVisionaryAgent() {
  const input = {
    seedIdea: "A young programmer accidentally creates a sentient agentic AI.",
    genre: "fiction",
    audience: "Adults 18-35",
    toneInspirations: "limitless",
    constraints: "Closed-room mystery. AI's power is exclusively psychological, affecting only nearby minds."
  };

  const storyCharter = await runVisionaryAgent(input);
  console.log(storyCharter);
}

// Test the Visionary agent
testVisionaryAgent();
