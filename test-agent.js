import { Agent } from './do.js';

async function testAgent() {
  console.log('Testing Agent class...');

  // Create a new agent with default configuration
  const agent = new Agent();

  // Test method chaining
  agent
    .setSystemInstruction('You are a helpful poetry assistant.')
    .setTemperature(0.8)
    .addUserMessage('Write a beautiful poem about a tabby cat.');

  console.log('Agent configuration:');
  console.log(`- Model: ${agent.model}`);
  console.log(`- System Instruction: ${agent.systemInstruction}`);
  console.log(`- Temperature: ${agent.temperature}`);
  console.log(`- Messages: ${JSON.stringify(agent.messages)}`);

  console.log('\nRunning agent with streaming...');
  await agent.runAndStream();

  console.log('\n\nTest completed successfully!');
}

testAgent().catch(console.error);
