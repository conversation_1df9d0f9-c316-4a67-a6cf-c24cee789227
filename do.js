import ollama from 'ollama'

const systemInstruction = {
  role: 'system',
  content: 'You are a helpful assistant.'
};

const message = { role: 'user', content: 'Write a beautiful poem about a tabby cat.' }

const response = await ollama.chat({
  model: 'gemma3:1b',
  messages: [systemInstruction, message],
  stream: true,
  temperature: 0.7,
})
for await (const part of response) {
  process.stdout.write(part.message.content)
}
