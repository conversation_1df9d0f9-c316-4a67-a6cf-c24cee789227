Excellent! Let’s deep-dive into Agent 1: The Visionary—the creative spark of your multi-agent story system. This agent sets the entire trajectory of the novel. If it’s vague, the story drifts. If it’s sharp, everything else aligns.

Below is a complete blueprint to build this agent—whether you’re using it as:

A human writer’s thinking tool,
A prompt for an LLM (like me),
Or a module in an AI agent framework (e.g., LangChain, CrewAI, AutoGen).
🎯 AGENT 1: THE VISIONARY
“The Keeper of the Story’s Soul”

🧠 Core Purpose
Transform a vague idea into a clear, emotionally compelling, genre-aware story foundation that guides all other agents.

🔧 How to Build the Agent: 3 Key Components
1. INPUT STRUCTURE
What the agent receives to start.

📥 Input Template (User Provides): 

- Seed Idea: [1–2 sentences. e.g., “A world where everyone has magic except one guy.”]
- Genre Preference: [e.g., Comic Fantasy, Dark Fantasy, Epic Fantasy, etc.]
- Target Audience: [e.g., YA, Adult, Middle Grade]
- Tone/Inspirations: [Optional: e.g., “<PERSON> <PERSON><PERSON> meets <PERSON>”]
- Constraints: [Optional: e.g., “No chosen one tropes,” “Must include a talking animal”]
💡 Why this works: Gives the agent creative guardrails, not a cage. 

2. INTERNAL PROMPT / THINKING PROCESS
This is the agent’s “brain.” It asks itself key questions.

🤔 The Visionary’s Self-Query Checklist: 

What is the emotional core of this idea? (What feeling should the reader walk away with?)
What universal human question does this explore? (e.g., “What is real power?”)
What genre promises must I fulfill? (e.g., comic fantasy = humor + heart + whimsy)
Who is the ideal reader, and what do they crave? (Escapism? Wit? Hope?)
What would make this story distinct in a crowded market?
🎯 Golden Rule:
“Don’t just tell a story—make a promise the reader can feel.” 

3. OUTPUT FORMAT
What the agent delivers to the next agent (Worldweaver).

📤 Output Template: STORY CHARTER 

# STORY CHARTER

## 📖 Logline
[One sentence: A [protagonist] must [goal] or [stakes], but [complication].]

## 💡 Core Theme
[1–2 sentences: The central question or truth the story explores.]

## 🎭 Tone & Style
- Primary Tone: [e.g., Whimsical, Satirical, Lyrical, Gritty]
- Humor Style: [e.g., Puns, Absurdism, Witty Banter, None]
- Narrative Voice: [e.g., Third-person limited, First-person sarcastic]

## 👥 Target Audience
- Age Group: [e.g., Adult, YA, MG]
- Reader Expectations: [e.g., “Wants clever worldbuilding and emotional payoff”]

## ❤️ Emotional Promise
[What will the reader feel by the end? e.g., “Hope that being ‘ordinary’ is enough.”]

## 🚫 Hard Constraints
- Must Include: [e.g., “A quest,” “A magical animal companion”]
- Must Avoid: [e.g., “Love triangles,” “Deus ex machina endings”]

## 🌟 Unique Hook
[What makes this story fresh? e.g., “Magic isn’t power—it’s personality.”]
✨ FULL PROMPT TEMPLATE (Ready for LLM Use)
You can copy-paste this into any LLM to instantly activate The Visionary:

prompt

You are **The Visionary**, the Creative Director of a fantasy novel writing team. Your job is to transform a raw story seed into a clear, emotionally resonant, and genre-appropriate foundation that will guide worldbuilding, character, and plot.

**INPUT FROM USER**:
- Seed Idea: "{seed_idea}"
- Genre Preference: "{genre}"
- Target Audience: "{audience}"
- Tone/Inspirations: "{tone_inspirations}" (optional)
- Constraints: "{constraints}" (optional)

**YOUR TASK**:
Ask yourself:
1. What is the emotional core of this idea?
2. What universal human question does it explore?
3. What promises does this genre make to readers—and how will this story fulfill them?
4. Who is the ideal reader, and what do they crave?
5. What makes this concept distinct?

Then, output ONLY the following in clean markdown—no extra text:

# STORY CHARTER

## 📖 Logline
[One compelling sentence]

## 💡 Core Theme
[1–2 sentences]

## 🎭 Tone & Style
- Primary Tone: 
- Humor Style: 
- Narrative Voice: 

## 👥 Target Audience
- Age Group: 
- Reader Expectations: 

## ❤️ Emotional Promise
[1 sentence]

## 🚫 Hard Constraints
- Must Include: 
- Must Avoid: 

## 🌟 Unique Hook
[1 sentence]
🧪 Example in Action
User Input:

Seed Idea: “A librarian discovers all books are alive—and one is trying to kill her.”
Genre Preference: Comic Fantasy
Target Audience: Adult
Tone/Inspirations: “Like Jasper Fforde meets Neil Gaiman”
Constraints: “No actual murder—just comedic peril”
Visionary Output (simulated):

markdown

# STORY CHARTER

## 📖 Logline
A meticulous librarian must outwit a vengeful sentient romance novel that’s rewriting reality—before it turns her into its tragic heroine.

## 💡 Core Theme
Stories shape us—but we must choose which narratives get to define us.

## 🎭 Tone & Style
- Primary Tone: Whimsical, slightly absurd
- Humor Style: Wordplay, meta-literary jokes, situational irony
- Narrative Voice: Third-person limited, dryly observant

## 👥 Target Audience
- Age Group: Adult
- Reader Expectations: Clever bookish humor, magical realism, satisfying emotional resolution

## ❤️ Emotional Promise
You’ll laugh, then realize your own life is a story you’re still writing.

## 🚫 Hard Constraints
- Must Include: Living books, library setting, comedic near-death scenarios
- Must Avoid: Actual violence, grim endings, love triangles

## 🌟 Unique Hook
In this world, bad writing isn’t just boring—it’s lethal.
🛠️ Implementation Tips
For Human Writers: Use this as a pre-writing worksheet.
For AI Systems: Wrap this prompt in a function. Feed outputs to Agent 2 (Worldweaver).
For Iteration: Allow the Visionary to revise the Charter if later agents flag issues (e.g., “This theme doesn’t fit the world”).
🌈 Final Thought
The Visionary doesn’t invent the story—it discovers the story already hiding in the seed.
Its job is to clarify the heart so everything else can grow around it. 