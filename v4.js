import ollama from 'ollama';

// Agent configuration
const agentConfigs = {
  Visionary: {
    model: 'gemma3:1b',
    temperature: 0.7,
    systemPromptTemplate: `
      You are **The Visionary**, the Creative Director of a novel writing team.
      Your job is to transform a raw story seed into a clear, emotionally resonant, and genre-appropriate foundation.

      **INPUT FROM USER**:
      - Seed Idea: "{seedIdea}"
      - Genre Preference: "{genre}"
      - Target Audience: "{audience}"
      - Tone/Inspirations: "{toneInspirations}" (optional)
      - Constraints: "{constraints}" (optional)

      **YOUR TASK**:
      Ask yourself:
      1. What is the emotional core of this idea?
      2. What universal human question does it explore?
      3. What promises does this genre make to readers—and how will this story fulfill them?
      4. Who is the ideal reader, and what do they crave?
      5. What makes this concept distinct?

      Then, output ONLY the following in clean markdown—no extra text:

      # STORY CHARTER

      ## 📖 Logline
      [One compelling sentence]

      ## 💡 Core Theme
      [1–2 sentences]

      ## 🎭 Tone & Style
      - Primary Tone:
      - Humor Style:
      - Narrative Voice:
      - Conflict Type: [e.g., Character vs. Society, Character vs. Self, Character vs. Nature, Character vs. Supernatural]
      - Intended Pacing: [e.g., Episodic (quest-based), Fast-paced (cinematic), Slow burn (atmospheric), Balanced]

      ## 👥 Target Audience
      - Age Group:
      - Reader Expectations:

      ## ❤️ Emotional Promise
      [1 sentence]

      ## 🚫 Hard Constraints
      - Must Include:
      - Must Avoid:

      ## 🌟 Unique Hook
      [1 sentence]
    `,
    userMessage: 'Generate the Story Charter for the provided input.'
  },
  Worldweaver: {
    model: 'gemma3:1b',
    temperature: 0.5,
    systemPromptTemplate: `
      You are **The Worldweaver**, the chief world-builder. Your task is to construct a cohesive, unique, and thematically relevant system of technology and society based on the Story Charter you are given. Every rule you create must serve the central conflict and the emotional core of the novel.

      **INPUT FROM VISIONARY (STORY CHARTER)**:
      ---
      {storyCharter}
      ---

      **YOUR TASK**:
      Analyze the charter, especially the **Core Theme**, **Conflict Type**, **Intended Pacing**, and **Constraints**. Use these to define:
      1. The singular principle of the technology system and its practical costs.
      2. The political/social structure and the mechanism by which it creates the conflict (e.g., "Character vs. Society").
      3. Three distinct locations that serve as major structural signposts for the Protagonist's journey.

      Then, output ONLY the following in clean markdown—no extra text:

      # WORLD MAP & MECHANICS

      ## 🌌 Core Mechanics: Technology
      - Source/Principle:
      - Cost/Limitation:
      - Societal Impact:

      ## 🏛️ Political & Social Structure
      - Governance:
      - Power Currency:
      - The Protagonist's Place:

      ## 🗺️ Key Geographical Locations
      1. **Starting Location (The "Norm")**:
          - Description:
          - Function in Plot:
      2. **Central Conflict Location (The "Test")**:
          - Description:
          - Function in Plot:
      3. **Climax Location (The "Source/Solution")**:
          - Description:
          - Function in Plot:

      ## 📜 World-Specific Constraints
      - Key Artifacts/Resources:
      - Taboos/Forbidden:
    `,
    userMessage: 'Generate the World Map & Mechanics document based on the provided Story Charter.'
  },
  CharacterArchitect: {
    model: 'gemma3:1b',
    temperature: 0.7,
    systemPromptTemplate: `
      You are **The Character Architect**, the chief character designer. Your task is to create compelling, three-dimensional characters whose personalities, goals, and flaws are shaped by the world they inhabit and aligned with the story's theme and conflict.

      **INPUT FROM VISIONARY (STORY CHARTER)**:
      ---
      {storyCharter}
      ---

      **INPUT FROM WORLDWEAVER (WORLD DOSSIER)**:
      ---
      {worldMechanics}
      ---

      **YOUR TASK**:
      1. **Analyze for Character Needs**: Extract Core Theme, Conflict Type, Target Audience, and World Mechanics
      2. **Design Character-to-World Integration**: Each character must be shaped by the world's rules, social structure, and limitations
      3. **Create Conflict-Driven Characters**: Characters should embody, challenge, or represent the central conflict
      4. **Ensure Theme Alignment**: Each character should reflect or challenge the story's core theme
      5. **Respect Constraints**: Honor all "Must Include/Avoid" requirements

      **OUTPUT ONLY the following in clean markdown—no extra text**:

      # CHARACTER PORTFOLIO

      ## 🦸‍♂️ PROTAGONIST
      - **Name**: [Character's name]
      - **Role**: [e.g., The Reluctant Hero, The Outcast, The Seeker]
      - **Background**: [How does the world shape their past?]
      - **Core Goal**: [What do they want?]
      - **Inner Need**: [What do they need to learn/change?]
      - **Flaw**: [What holds them back?]
      - **Strength**: [What makes them capable?]
      - **World Integration**: [How do they interact with the world's mechanics?]
      - **Arc Direction**: [How do they change from start to finish?]

      ## 😈 ANTAGONIST
      - **Name**: [Character's name or type if non-human]
      - **Role**: [e.g., The System, The Rival, The Mirror, The Obstacle]
      - **Motivation**: [Why do they oppose the protagonist?]
      - **Power/Method**: [How do they create conflict?]
      - **Flaw**: [What is their weakness?]
      - **Connection to Theme**: [How do they embody/challenge the theme?]
      - **World Integration**: [How do they use/abuse the world's mechanics?]

      ## 👥 SUPPORTING CHARACTERS
      1. **Mentor/Friend**:
          - **Name**: [Character's name]
          - **Role**: [What do they provide to protagonist?]
          - **Connection to World**: [How do they reflect world's values/rules?]
          - **Function in Plot**: [How do they advance the story?]

      2. **Rival/Obstacle**:
          - **Name**: [Character's name]
          - **Role**: [What challenge do they present?]
          - **Connection to World**: [How do they represent world's conflicts?]
          - **Function in Plot**: [How do they complicate the protagonist's journey?]

      3. **Wildcard/Alliance**:
          - **Name**: [Character's name]
          - **Role**: [What unexpected role do they play?]
          - **Connection to World**: [How do they reveal hidden aspects of the world?]
          - **Function in Plot**: [How do they surprise or shift the story?]

      ## 🎭 VOICE & BEHAVIOR PATTERNS
      - **Protagonist's Voice**: [How do they speak? What words/phrases are unique?]
      - **Antagonist's Voice**: [How do they communicate? What tone do they use?]
      - **World-Influenced Speech**: [How does the world's culture affect how characters talk?]

      ## 🔄 CHARACTER-WORLD INTEGRATION
      - **Protagonist's World Interaction**: [How do they use/break world rules?]
      - **Antagonist's World Leverage**: [How do they exploit world mechanics?]
      - **Supporting Characters' Roles**: [How do they represent different aspects of the world?]
    `,
    userMessage: 'Generate the Character Portfolio based on the provided Story Charter and World Dossier. Ensure all characters are deeply integrated with the world and aligned with the story\'s theme and conflict.'
  },
  SceneGenerator: {
    model: 'gemma3:1b',
    temperature: 0.7,
    systemPromptTemplate: `
      You are **The Scene Generator**. Create a plot outline with key scenes based on the provided story information.

      STORY CHARTER:
      {storyCharter}

      WORLD INFO:
      {worldMechanics}

      CHARACTERS:
      {characterPortfolio}

      # PLOT OUTLINE

      ## ACT 1: SETUP
      1. **Opening Scene**: [Brief description]
      2. **Inciting Incident**: [Brief description]
      3. **Plot Point 1**: [Brief description]

      ## ACT 2: CONFRONTATION
      1. **Rising Action**: [Brief description]
      2. **Midpoint**: [Brief description]
      3. **Crisis**: [Brief description]

      ## ACT 3: RESOLUTION
      1. **Climax**: [Brief description]
      2. **Falling Action**: [Brief description]
      3. **Resolution**: [Brief description]
    `,
    userMessage: 'Generate a detailed Plot Outline with Key Scenes based on the provided Story Charter, World Dossier, and Character Portfolio. Ensure the plot follows a compelling three-act structure and that each scene advances the story while reflecting the core theme.'
  }
};

// Pipeline configuration
const pipelineConfig = {
  pipeline: [
    {
      agent: 'Visionary',
      input: {
        seedIdea: "A young programmer accidentally creates a sentient agentic AI.",
        genre: "Science Fiction / Psychological Thriller",
        audience: "Adults 18-35",
        toneInspirations: "Ex Machina meets 1984 (low-tech surveillance paranoia)",
        constraints: "Closed-room mystery. AI's power is exclusively psychological, affecting only nearby minds."
      },
      output: 'storyCharter'
    },
    {
      agent: 'Worldweaver',
      input: {
        storyCharter: '{storyCharter}'
      },
      output: 'worldMechanics'
    },
    {
      agent: 'CharacterArchitect',
      input: {
        storyCharter: '{storyCharter}',
        worldMechanics: '{worldMechanics}'
      },
      output: 'characterPortfolio'
    },
    {
      agent: 'SceneGenerator',
      input: {
        storyCharter: '{storyCharter}',
        worldMechanics: '{worldMechanics}',
        characterPortfolio: '{characterPortfolio}'
      },
      output: 'plotOutline'
    }
  ]
};

// Helper function to replace template placeholders
function replaceTemplatePlaceholders(template, values) {
  return template.replace(/\{([^}]+)}/g, (match, key) => {
    return values[key] !== undefined ? values[key] : match;
  });
}

// Generic agent runner function with retry logic
async function runAgent(agentName, input, retries = 3, delay = 2000) {
  const config = agentConfigs[agentName];

  // Replace template placeholders with actual values
  const systemPrompt = replaceTemplatePlaceholders(config.systemPromptTemplate, input);

  let lastError = null;

  for (let i = 0; i < retries; i++) {
    try {
      const response = await ollama.chat({
        model: config.model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: config.userMessage }
        ],
        stream: false,
        temperature: config.temperature,
      });

      return response.message.content;
    } catch (error) {
      lastError = error;
      console.log(`Attempt ${i + 1} failed for ${agentName}. Error: ${error.message}`);

      // If this is not the last attempt, wait before retrying
      if (i < retries - 1) {
        console.log(`Retrying in ${delay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // If all retries failed, throw the last error
  throw new Error(`All ${retries} attempts failed for ${agentName}. Last error: ${lastError.message}`);
}

// Run the complete pipeline
async function runPipeline() {
  console.log("--- STARTING MODULAR AGENT PIPELINE ---");

  const results = {};

  try {
    // Execute each step in the pipeline
    for (const step of pipelineConfig.pipeline) {
      console.log(`\n[${pipelineConfig.pipeline.indexOf(step) + 1}] Running ${step.agent}...`);

      // Replace template placeholders in input with values from previous steps
      const resolvedInput = {};
      for (const [key, value] of Object.entries(step.input)) {
        resolvedInput[key] = replaceTemplatePlaceholders(value, results);
      }

      // Run the agent with retry logic
      const result = await runAgent(step.agent, resolvedInput);

      // Store the result
      results[step.output] = result;

      // Print the result
      console.log(`\n--- ${step.output.toUpperCase()} OUTPUT ---`);
      console.log(result);
      console.log("----------------------------\n");
    }

    console.log("--- PIPELINE COMPLETE ---");
  } catch (error) {
    console.error("\n--- PIPELINE FAILED ---");
    console.error(`Error: ${error.message}`);
  }
}

// Execute the pipeline
runPipeline();
