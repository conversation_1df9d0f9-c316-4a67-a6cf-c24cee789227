import ollama from 'ollama';
import * as readline from 'readline';

const MODEL_NAME = 'gemma3:1b';
const SYSTEM_PROMPT = 'You are a helpful assistant. Keep your answers concise.';
const EXIT_COMMAND = 'exit';

// 1. Initialize message history with the system instruction
const messages = [
  { role: 'system', content: SYSTEM_PROMPT }
];

// 2. Create the readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// The main function to prompt the user and process the response
const chatLoop = () => {
  // Use rl.question to get user input
  rl.question('You: ', async (userInput) => {
    
    // Check for exit command
    if (userInput.toLowerCase() === EXIT_COMMAND) {
      console.log('Assistant: Goodbye!');
      rl.close();
      return;
    }

    // 3. Add the new user message to the history
    messages.push({ role: 'user', content: userInput });
    
    // Display the Assistant's prompt
    process.stdout.write('Assistant: ');

    try {
      // 4. Call Ollama with the entire message history
      const stream = await ollama.chat({
        model: MODEL_NAME,
        messages: messages, // Send all previous messages for context
        stream: true,
        temperature: 0.7,
      });

      let fullResponse = '';
      
      // Stream the response to the console
      for await (const chunk of stream) {
        if (chunk.message) {
          const content = chunk.message.content;
          process.stdout.write(content);
          fullResponse += content;
        }
      }
      
      // 5. Add a newline after the streamed response is complete
      console.log(); 

      // 6. Add the complete assistant response to the history for the next turn
      messages.push({ role: 'assistant', content: fullResponse });

    } catch (error) {
      console.error('\nAn error occurred while communicating with Ollama:', error.message);
    }
    
    // 7. Call the loop again for the next turn
    chatLoop();
  });
};

// Start the chat
console.log(`\n--- Ollama Chat CLI ---`);
console.log(`Model: ${MODEL_NAME}`);
console.log(`Type "${EXIT_COMMAND}" to end the chat.\n`);
chatLoop();